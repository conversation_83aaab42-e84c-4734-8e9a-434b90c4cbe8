# 天玑 GPU 调速器 🚀

## 简介 📝

天玑 GPU 调速器（Mediatek Mali GPU Governor）是一个专为联发科处理器设计的 GPU 频率调节工具，通过智能监控 GPU 负载并动态调整频率，提供更好的游戏体验和功耗平衡。基于 Rust 语言开发，具有高效、稳定的特点。

## 特性 ✨

- 🎮 **游戏模式**：自动识别游戏应用并应用优化的 GPU 频率设置
- 📊 **负载监控**：实时监控 GPU 负载并根据需求动态调整频率
- ⚡ **性能优化**：在需要时提供高性能，在空闲时降低频率节省电量
- 🔧 **高度可定制**：通过配置文件自定义 GPU 频率和电压
- 📱 **广泛兼容**：支持多种联发科处理器平台，包括Dimensity系列
- 🖥️ **WebUI 界面**：基于 KernelSU 的图形化管理界面，支持深色/浅色模式，使用Miuix风格
- 📝 **多级日志**：支持 debug、info、warn、error 四个日志等级，方便调试
- 🔄 **多级负载阈值**：支持极低、低、中、高、极高五个负载区域的智能调频
- 🌐 **多语言支持**：支持中文和英文界面，自动检测系统语言设置

## 安装要求 📋

- 已 Root 的 Android 设备
- 搭载联发科处理器（MTK）的设备，支持Mali GPU
- Magisk v20.4+ 或 KernelSU 或 APatch 等支持模块的 Root 方案
- 使用 WebUI 功能需要 KernelSU, APatch 或安装 [KsuWebUI](https://github.com/5ec1cff/KsuWebUIStandalone)/[MMRL](https://github.com/MMRLApp/MMRL) 应用

## 安装方法 💻

1. 在 Magisk 或 KernelSU 中安装此模块
2. 重启设备
3. 模块将自动配置并启动 GPU 调速服务
4. 安装完成后，模块会自动识别设备型号并应用适合的配置
5. 如果设备型号不受支持，会使用默认配置文件

## 配置文件 ⚙️

### GPU 频率表配置

配置文件位于 `/data/gpu_freq_table.conf`，格式如下：

```
# Margin: 调整GPU频率计算的余量百分比
Margin=5
# Freq Volt DDR_OPP
218000 45000 999
280000 46875 999
350000 48750 999
431000 49375 999
471000 50625 999
532000 51875 999
573000 53125 2
634000 55000 1
685000 56875 1
755000 59375 0
853000 60625 0
```

- **Freq**: GPU 频率 (KHz)
- **Volt**: 电压 (μV)
- **DDR_OPP**: 内存频率档位（999表示不调整，0-3表示不同档位）
- **Margin**: 频率计算余量百分比（可通过WebUI或直接编辑配置文件调整）

### 游戏列表配置

游戏列表配置文件位于 `/data/adb/gpu_governor/game/games.conf`，包含需要应用游戏模式的应用包名。模块会在安装时自动扫描设备上已安装的游戏并生成此配置文件。

**注意**：安装脚本会检查游戏列表文件是否已存在，如果存在则不会覆盖，以保留用户的自定义设置。

### 交互式控制菜单

模块提供了 `action.sh` 脚本，可以通过音量键进行交互式操作：

```
# 直接执行脚本，无需参数
./action.sh
```

脚本会显示交互式菜单，可以通过音量键进行以下操作：
- **切换游戏模式**：开启或关闭游戏模式
- **控制调速器服务**：启动或停止 GPU 调速器服务
- **设置日志等级**：选择 debug、info、warn 或 error 级别

脚本会自动检测当前系统语言，并显示相应的中文或英文界面。

游戏模式状态保存在 `/data/adb/gpu_governor/game/game_mode` 文件中，值为 `1` 表示开启，`0` 表示关闭。

### 日志等级设置

日志等级设置保存在 `/data/adb/gpu_governor/log/log_level` 文件中，默认为 `info` 级别。可以通过以下三种方式进行设置：
1. 使用交互式菜单 `./action.sh` 选择日志等级
2. 通过 WebUI 界面的设置页面进行调整
3. 直接编辑 `/data/adb/gpu_governor/log/log_level` 文件

修改日志等级后立即生效，无需重启模块。

### 负载阈值设置

模块内部实现了多级负载阈值，用于智能调整GPU频率：

- **极低负载**: 默认10%以下，降低频率以节省电量
- **低负载**: 默认10-30%，适当降低频率
- **中等负载**: 默认30-70%，保持平衡的频率
- **高负载**: 默认70-90%，提高频率以提供更好性能
- **极高负载**: 默认90%以上，使用最高频率

游戏模式下使用更积极的升频策略，负载阈值为5/20/60/85，更快进入高负载区域，提供更好的游戏体验。这种设计使得在游戏中能够更快地响应负载变化，提供更流畅的游戏体验。

## 日志系统 📊

日志文件存储在 `/data/adb/gpu_governor/log/` 目录下，主要包括：

- **gpu_gov.log**: 主日志文件，记录 GPU 调速器的运行状态和频率调整记录
- **initsvc.log**: 初始化服务日志，记录模块启动过程

日志文件大小限制为 5MB，超过限制会自动轮转，防止占用过多存储空间。轮转时会将原日志文件备份为 `.bak` 文件，并创建新的日志文件。日志内容可以通过WebUI界面查看，也可以通过文件管理器直接查看。

### 日志等级

模块支持四个日志等级，可以通过 `action.sh` 脚本或 WebUI 界面进行设置：

- **debug**: 调试级别，记录所有详细信息，包括频率调整、负载监控等
- **info**: 信息级别，记录正常运行信息，默认级别
- **warn**: 警告级别，只记录警告和错误信息
- **error**: 错误级别，只记录错误信息

日志等级设置保存在 `/data/adb/gpu_governor/log/log_level` 文件中，修改后立即生效，无需重启模块。

### 日志轮转机制

为了防止日志文件过大占用存储空间，模块实现了自动日志轮转机制：

1. 当日志文件大小达到最大限制（5MB）的80%时，会自动进行轮转
2. 轮转时会将当前日志文件备份为 `.bak` 文件，并创建新的空日志文件
3. 每次模块启动时也会检查日志大小并进行必要的轮转

## 卸载方法 🗑️

通过 root管理器 卸载模块，或执行 `uninstall.sh` 脚本清理配置文件。卸载时会自动删除以下文件：

- `/data/gpu_freq_table.conf`
- `/data/adb/gpu_governor/` 目录及其所有内容

## 支持的设备 📱

支持大多数搭载 Mali GPU 的联发科处理器，包括但不限于：
- Dimensity 系列（如 D700/D800/D900/D1000/D8000/D9000 等）
- Helio 系列
- MT6xxx 系列

模块会自动检测设备型号并应用适合的配置。
如果您的设备不在支持列表中，模块会使用默认配置。您也可以手动编辑配置文件来适配您的设备。

## 致谢 🙏

- **作者**: 瓦力喀 @CoolApk, rtools @CoolApk
- **特别感谢**: HamJin @CoolApk, asto18089 @CoolApk, helloklf @Github
- **测试反馈**: 内测群全体群友
- **配置协助**: Fiagelia @CoolApk, 忘渐 @CoolApk

## 注意事项 ⚠️

- 修改 GPU 频率和电压可能会影响设备稳定性
- 不当的配置可能导致设备过热或性能问题
- 建议备份原始配置文件以便恢复
- 如遇到问题，可以查看日志文件进行排查

## WebUI 界面 🖥️

本模块提供了基于 KernelSU 的 WebUI 界面，方便用户直观地管理和监控 GPU 调速器。WebUI使用Miuix风格设计，支持深色/浅色模式和多语言。

### 访问方式

#### KernelSU/APatch 用户
1. 确保已安装 KernelSU/APatch 并启用了 WebUI 功能
2. 在 KernelSU/APatch 管理器中点击本模块，选择"打开 WebUI"

#### Magisk 用户
Magisk 用户可以通过以下方式访问 WebUI：
1. 安装 [KsuWebUI](https://github.com/5ec1cff/KsuWebUIStandalone) 应用
2. 或使用 [MMRL](https://github.com/MMRLApp/MMRL) 应用打开模块的 WebUI

### 功能特性

- **实时状态监控**：查看模块运行状态、版本信息和游戏模式状态
- **GPU 频率配置**：查看和编辑当前 GPU 频率表配置，支持调整频率、电压和内存档位
- **游戏列表管理**：查看和编辑已配置的游戏列表
- **日志查看**：实时查看模块运行日志，支持选择不同日志文件和日志等级
- **深色模式支持**：自动适应系统深色/浅色模式，也可手动切换
- **日志等级设置**：支持在WebUI中直接设置日志等级
- **多语言支持**：支持中文和英文界面，自动检测系统语言设置
- **电压调整**：支持使用旋转选择器进行电压调整，长按可连续调整（每次±625单位）
- **游戏模式实时更新**：每秒检测游戏模式状态变化并更新界面

### 界面布局

WebUI 提供了多页面布局，通过底栏进行页面切换，包括以下几个主要部分：

- **状态页面**：显示模块运行状态、版本信息和游戏模式开关
- **配置页面**：显示和编辑当前配置的频率、电压和内存频率档位，以及管理游戏列表和余量设置
- **日志页面**：显示最近的运行日志，支持选择不同日志文件和日志等级
- **设置页面**：提供主题设置、语言设置和日志等级设置等选项

## 常见问题 ❓

**Q: 如何确认模块正常工作？**
A: 查看 `/data/adb/gpu_governor/log/gpu_gov.log` 日志文件，确认有正常的频率调节记录。或者通过 WebUI 界面查看模块状态和日志。正常工作时，日志中应该有 GPU 负载和频率调整的记录。

**Q: 游戏模式如何工作？**
A: 游戏模式有两种激活方式：1) 当检测到 `games.conf` 中列出的应用运行时，会自动应用游戏模式；2) 通过 `action.sh` 脚本或 WebUI 手动开启。游戏模式下会使用更积极的升频策略（负载阈值为5/20/60/85），更快进入高负载区域，提供更好的游戏体验。

**Q: 如何添加自定义游戏到列表？**
A: 编辑 `/data/adb/gpu_governor/game/games.conf` 文件，添加游戏的包名即可。或者通过 WebUI 界面的游戏列表页面进行添加。模块会在安装时自动扫描设备上已安装的游戏并生成初始游戏列表。

**Q: 如何调整日志等级？**
A: 有三种方式：1) 使用交互式菜单 `./action.sh` 选择日志等级；2) 通过 WebUI 界面的设置页面进行调整；3) 直接编辑 `/data/adb/gpu_governor/log/log_level` 文件。调整后会立即生效，无需重启模块。

**Q: 如何使用 WebUI？**
A: KernelSU/APatch 用户可在root管理器中点击本模块，选择"打开 WebUI"。Magisk 用户可安装 [KsuWebUI](https://github.com/5ec1cff/KsuWebUIStandalone) 或 [MMRL](https://github.com/MMRLApp/MMRL) 应用来访问模块的 WebUI。

**Q: 如何调整GPU频率计算的余量？**
A: 在 `/data/gpu_freq_table.conf` 文件中添加或修改 `Margin=数值` 行，数值表示余量百分比。也可以通过 WebUI 界面的配置页面进行调整。余量越大，实际频率越高，性能越好但功耗也越高。
