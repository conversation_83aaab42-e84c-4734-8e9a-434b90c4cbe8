# 📋 天玑GPU调速器 更新日志

## 🚀 v2.5 → v2.6 (2025-05-19 → 2025-06-01)

### ✨ 新增功能

#### 📚 文档系统优化
- 🌐 **新增英文文档支持**：在 `docs/en/README.md` 中添加了完整的英文版本文档
- 📖 **文档结构重组**：优化了文档布局，增强了可读性和导航性
- 🔗 **社区链接**：在文档顶部添加了 Discord 和 Telegram 社区链接徽章
- 📝 **详细功能说明**：扩展了 WebUI 功能特性的详细描述

#### 🎛️ 交互式控制优化
- 🎮 **简化操作菜单**：移除了手动游戏模式切换选项，专注于自动游戏检测
- 🔧 **精简控制脚本**：`action.sh` 脚本功能更加专注，提升用户体验
- ⚙️ **菜单选项调整**：主菜单从4个选项精简为3个选项（调速器服务控制、日志等级设置、退出）

### 🔄 功能变更

#### 🎮 游戏模式管理
- ❌ **移除手动游戏模式切换**：不再支持通过 `action.sh` 脚本手动开启/关闭游戏模式
- 🤖 **专注自动检测**：游戏模式现在完全依赖自动检测 `games.conf` 中的应用
- 📱 **WebUI 显示保留**：游戏模式仍可通过 WebUI 界面进行显示

#### 📋 脚本功能调整
- 🔧 **action.sh 功能精简**：
  - ✅ 保留：调速器服务控制（启动/停止）
  - ✅ 保留：日志等级设置
  - ✅ 保留：模块状态查看
  - ❌ 移除：手动游戏模式切换功能
- 🎯 **操作流程优化**：简化了用户操作流程，减少了选择复杂度

### 📖 文档改进

#### 🌍 多语言支持
- 🇺🇸 **完整英文文档**：新增 `docs/en/README.md` 提供完整的英文版本说明
- 🔄 **文档同步**：中英文文档内容保持同步更新

### 🔧 技术改进

#### 🛠️ 脚本优化
- 🎛️ **菜单逻辑简化**：`action.sh` 中的菜单选择逻辑更加简洁
- 🔢 **选项编号调整**：主菜单选项重新编号以适应功能变更
- ⏱️ **超时处理保持**：保留了30秒超时自动退出机制

### 📊 用户体验提升

#### 🎯 操作简化
- 🎮 **游戏模式自动化**：用户无需手动切换游戏模式，系统自动检测并应用
- 🔧 **核心功能聚焦**：控制脚本专注于服务管理和日志配置
- 📱 **WebUI 为主**：推荐用户使用 WebUI 进行详细配置和管理

#### 📚 文档可访问性
- 🌐 **国际化支持**：英文用户现在有完整的英文文档可参考
- 🔍 **信息查找**：改进的文档结构使用户更容易找到所需信息
- 💬 **社区支持**：提供了直接的社区链接，便于用户获取帮助

### 📍 文件路径信息

#### 📝 日志文件位置
- 主日志：`/data/adb/gpu_governor/log/gpu_gov.log`
- 初始化日志：`/data/adb/gpu_governor/log/initsvc.log`
- 日志等级配置：`/data/adb/gpu_governor/log/log_level`

#### ⚙️ 配置文件位置
- GPU频率表：`/data/gpu_freq_table.conf`
- 游戏列表：`/data/adb/gpu_governor/game/games.conf`
- 游戏模式状态：`/data/adb/gpu_governor/game/game_mode`

#### 📚 文档文件位置
- 中文文档：`docs/README.md`
- 英文文档：`docs/en/README.md`

---

### 🎯 升级建议

对于从 v2.5 升级到 v2.6 的用户：

1. 🎮 **游戏模式使用**：如果之前依赖手动切换游戏模式，请改用 WebUI 界面或确保游戏已添加到 `games.conf` 文件中
2. 📱 **WebUI 推荐**：建议使用 WebUI 界面进行详细的配置管理，获得更好的用户体验
3. 📖 **文档查阅**：查看新的文档结构，了解最新的功能特性和使用方法
4. 💬 **社区参与**：加入 Discord 或 Telegram 社区获取最新信息和技术支持

---

*📅 更新时间：2025年6月1日*  
*👨‍💻 维护者：瓦力喀 @CoolApk*
