<!DOCTYPE html>
<html lang="zh-CN" id="htmlRoot">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天玑GPU调速器</title>
    <link rel="stylesheet" href="styles.css">
    <!-- 使用本地字体或系统字体，避免网络请求 -->
</head>
<body>
    <div id="loading">加载中</div>
    <div id="app" style="display: none;">
        <header class="app-header">
            <div class="header-content">
                <h1>天玑GPU调速器</h1>
                <div class="theme-toggle" id="themeToggle">
                    <span class="light-icon">☀️</span>
                    <span class="dark-icon">🌙</span>
                </div>
            </div>
        </header>

        <main class="container">
            <!-- 页面容器 -->
            <div class="page-container">
                <!-- 页面1: 状态 -->
                <div class="page" id="page-status">
                    <!-- 模块状态卡片 -->
                    <section class="card" id="statusCard">
                        <h2 class="card-title">模块状态</h2>
                        <div class="card-content">
                            <div class="status-item">
                                <span>运行状态:</span>
                                <span id="runningStatus" class="status-badge">检查中...</span>
                            </div>
                            <div class="status-item">
                                <span>游戏模式:</span>
                                <span id="gameModeStatus" class="status-badge">检查中...</span>
                            </div>
                            <div class="status-item">
                                <span>模块版本:</span>
                                <span id="moduleVersion" class="version-badge">加载中...</span>
                            </div>
                        </div>
                    </section>

                    <!-- 版权信息卡片 -->
                    <section class="card" id="copyrightCard">
                        <div class="card-content copyright-content">
                            <p>天玑GPU调速器 © 2025 酷安@瓦力喀</p>
                        </div>
                    </section>
                </div>

                <!-- 页面2: 配置 -->
                <div class="page" id="page-config">
                    <!-- GPU配置表卡片 -->
                    <section class="card" id="gpuConfigCard">
                        <h2 class="card-title">GPU配置表</h2>
                        <div class="card-content">
                            <div class="table-container">
                                <table id="gpuFreqTable">
                                    <thead>
                                        <tr>
                                            <th>频率 (MHz)</th>
                                            <th>电压 (uV)</th>
                                            <th>内存档位</th>
                                            <th>编辑</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td colspan="4" class="loading-text">加载中...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="config-actions">
                                <button id="addConfigBtn" class="btn">添加配置</button>
                                <button id="saveConfigBtn" class="btn">保存配置</button>
                            </div>
                        </div>
                    </section>

                    <!-- GPU余量配置卡片 -->
                    <section class="card" id="marginCard">
                        <h2 class="card-title">GPU频率计算余量</h2>
                        <div class="card-content">
                            <div class="status-item margin-setting">
                                <span>余量百分比 (%):</span>
                                <div class="number-spinner">
                                    <button class="spinner-btn" id="marginDecreaseBtn">-</button>
                                    <div class="spinner-value" id="marginValue">20</div>
                                    <button class="spinner-btn" id="marginIncreaseBtn">+</button>
                                </div>
                            </div>
                            <div class="setting-description">
                                <small>余量越大，GPU升频越积极，性能越充裕</small>
                                <small>游戏模式下会自动增加10%的余量</small>
                            </div>
                            <div class="margin-actions">
                                <button id="saveMarginBtn" class="btn">保存余量设置</button>
                            </div>
                        </div>
                    </section>

                    <!-- 游戏列表卡片 -->
                    <section class="card" id="gamesCard">
                        <h2 class="card-title">游戏列表</h2>
                        <div class="card-content">
                            <ul id="gamesList" class="games-list">
                                <li class="loading-text">加载中...</li>
                            </ul>
                            <div class="games-actions">
                                <button id="addGameBtn" class="btn">添加游戏</button>
                                <button id="saveGamesBtn" class="btn">保存列表</button>
                            </div>
                        </div>
                    </section>
                </div>

                <!-- 页面3: 日志 -->
                <div class="page" id="page-log">
                    <!-- 日志卡片 -->
                    <section class="card" id="logCard">
                        <h2 class="card-title">运行日志</h2>
                        <div class="card-content">
                            <div class="log-actions">
                                <button id="refreshLogBtn" class="btn">刷新</button>
                                <div class="select-container">
                                    <div class="custom-select" id="logFileContainer">
                                        <div class="selected-option" id="selectedLogFile">主日志</div>
                                        <div class="options-container" id="logFileOptions">
                                            <div class="option" data-value="gpu_gov.log">主日志</div>
                                            <div class="option" data-value="initsvc.log">初始化日志</div>
                                        </div>
                                    </div>
                                    <select id="logFileSelect" class="select" style="display: none;">
                                        <option value="gpu_gov.log">主日志</option>
                                        <option value="initsvc.log">初始化日志</option>
                                    </select>
                                </div>
                            </div>
                            <pre id="logContent" class="log-content">加载中...</pre>
                        </div>
                    </section>
                </div>

                <!-- 页面4: 设置 -->
                <div class="page" id="page-settings">
                    <!-- 设置卡片 -->
                    <section class="card" id="settingsCard">
                        <h2 class="card-title">设置</h2>
                        <div class="card-content">
                            <div class="status-item">
                                <span>深色模式跟随系统:</span>
                                <label class="switch">
                                    <input type="checkbox" id="followSystemThemeToggle">
                                    <span class="slider round"></span>
                                </label>
                            </div>

                            <div class="status-item">
                                <span>语言设置:</span>
                                <div class="select-container">
                                    <div class="custom-select" id="languageContainer">
                                        <div class="selected-option" id="selectedLanguage">跟随系统</div>
                                        <div class="options-container" id="languageOptions">
                                            <div class="option" data-value="system">跟随系统</div>
                                            <div class="option" data-value="zh">中文</div>
                                            <div class="option" data-value="en">English</div>
                                        </div>
                                    </div>
                                    <select id="languageSelect" class="select" style="display: none;">
                                        <option value="system">跟随系统</option>
                                        <option value="zh">中文</option>
                                        <option value="en">English</option>
                                    </select>
                                </div>
                            </div>
                            <div class="setting-description">
                                <small>修改语言设置后实时生效</small>
                                <small>跟随系统将自动检测系统语言设置</small>
                            </div>

                            <div class="status-item">
                                <span>主日志等级:</span>
                                <div class="select-container">
                                    <div class="custom-select" id="logLevelContainer">
                                        <div class="selected-option" id="selectedLogLevel">Info (信息)</div>
                                        <div class="options-container" id="logLevelOptions">
                                            <div class="option" data-value="debug">Debug (详细)</div>
                                            <div class="option" data-value="info">Info (信息)</div>
                                            <div class="option" data-value="warn">Warn (警告)</div>
                                            <div class="option" data-value="error">Error (错误)</div>
                                        </div>
                                    </div>
                                    <select id="logLevelSelect" class="select" style="display: none;">
                                        <option value="debug">Debug (详细)</option>
                                        <option value="info">Info (信息)</option>
                                        <option value="warn">Warn (警告)</option>
                                        <option value="error">Error (错误)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="setting-description">
                                <small>修改日志等级后实时生效</small>
                                <small>设置为Debug级别将启用详细日志记录</small>
                            </div>
                        </div>
                    </section>
                </div>
            </div>

            <!-- 编辑配置对话框 -->
            <div id="editConfigModal" class="modal">
                <div class="modal-content">
                    <span class="close-modal">&times;</span>
                    <h3>编辑GPU配置</h3>
                    <div class="form-group">
                        <label for="freqInput">频率 (MHz):</label>
                        <input type="number" id="freqInput" min="100000" max="1000000" step="1000" placeholder="例如: 350000 (KHz)">
                        <small class="input-hint">请输入KHz值，表格中显示为MHz</small>
                    </div>
                    <div class="form-group">
                        <label for="voltSelect">电压 (uV):</label>
                        <div class="number-spinner">
                            <button class="spinner-btn" id="voltDecreaseBtn">-</button>
                            <div class="spinner-value" id="selectedVolt">65000</div>
                            <button class="spinner-btn" id="voltIncreaseBtn">+</button>
                            <select id="voltSelect" class="select" style="display: none;">
                                <!-- 电压选项将通过JavaScript动态添加 -->
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="ddrSelect">内存档位:</label>
                        <div class="select-container">
                            <div class="custom-select" id="ddrContainer">
                                <div class="selected-option" id="selectedDdr">999 (不调整)</div>
                                <div class="options-container" id="ddrOptions">
                                    <div class="option" data-value="999">999 (不调整)</div>
                                    <div class="option" data-value="0">0 (最高)</div>
                                    <div class="option" data-value="1">1</div>
                                    <div class="option" data-value="2">2</div>
                                    <div class="option" data-value="3">3 (最低)</div>
                                </div>
                            </div>
                            <select id="ddrSelect" class="select" style="display: none;">
                                <option value="999">999 (不调整)</option>
                                <option value="0">0 (最高)</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3 (最低)</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button id="saveItemBtn" class="btn">保存</button>
                        <button id="cancelEditBtn" class="btn btn-secondary">取消</button>
                        <button id="deleteItemBtn" class="btn btn-danger">删除</button>
                    </div>
                </div>
            </div>

            <!-- 编辑游戏对话框 -->
            <div id="editGameModal" class="modal">
                <div class="modal-content">
                    <span class="close-game-modal">&times;</span>
                    <h3>编辑游戏列表</h3>
                    <div class="form-group">
                        <label for="packageNameInput">应用包名:</label>
                        <input type="text" id="packageNameInput" placeholder="例如: com.tencent.tmgp.sgame">
                    </div>
                    <div class="form-actions">
                        <button id="saveGameBtn" class="btn">保存</button>
                        <button id="cancelGameBtn" class="btn btn-secondary">取消</button>
                    </div>
                </div>
            </div>
        </main>

        <!-- 底部导航栏 -->
        <nav class="nav-bar">
            <button class="nav-item active" data-page="page-status">
                <span class="nav-icon">📊</span>
                <span class="nav-text">状态</span>
            </button>
            <button class="nav-item" data-page="page-config">
                <span class="nav-icon">⚙️</span>
                <span class="nav-text">配置</span>
            </button>
            <button class="nav-item" data-page="page-log">
                <span class="nav-icon">📝</span>
                <span class="nav-text">日志</span>
            </button>
            <button class="nav-item" data-page="page-settings">
                <span class="nav-icon">🔧</span>
                <span class="nav-text">设置</span>
            </button>
        </nav>

        <!-- 移除页脚，版权信息已移至状态页面 -->
    </div>

    <script src="app.js"></script>
</body>
</html>
