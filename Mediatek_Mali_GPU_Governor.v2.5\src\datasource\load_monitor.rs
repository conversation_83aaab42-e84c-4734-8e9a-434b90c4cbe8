use std::{
    fs::File,
    io::{<PERSON><PERSON><PERSON><PERSON>, <PERSON>uf<PERSON>eader},
};

use anyhow::{anyhow, Context, Result};
use log::{debug, error, info};

use crate::{
    datasource::file_path::*,
    utils::{
        file_operate::{check_read, read_file},
        file_status::{get_status, write_status},
    },
};

fn module_ged_load() -> Result<i32> {
    if !get_status(MODULE_LOAD) {
        return Ok(-1);
    }

    let buf = read_file(MODULE_LOAD, 32)?;
    let load = buf
        .trim()
        .parse::<i32>()
        .with_context(|| format!("Failed to parse GPU load from {MODULE_LOAD}"))?;

    Ok(load)
}

fn module_ged_idle() -> Result<i32> {
    if !get_status(MODULE_IDLE) {
        return module_ged_load();
    }

    let buf = read_file(MODULE_IDLE, 32)?;
    let idle = buf
        .trim()
        .parse::<i32>()
        .with_context(|| format!("Failed to parse GPU idle from {MODULE_IDLE}"))?;

    let load = 100 - idle;
    debug!("module {load}");
    Ok(100 - idle)
}

fn kernel_ged_load() -> Result<i32> {
    if !get_status(KERNEL_LOAD) {
        return module_ged_idle();
    }

    let buf = read_file(KERNEL_LOAD, 32)?;
    let parts: Vec<&str> = buf.split_whitespace().collect();

    if parts.len() >= 3 {
        if let Ok(idle) = parts[2].parse::<i32>() {
            let load = 100 - idle;
            debug!("gedload {load}");
            return Ok(if 100 - idle == 0 {
                module_ged_load()?
            } else {
                100 - idle
            });
        }
    }

    module_ged_idle()
}

fn kernel_debug_ged_load() -> Result<i32> {
    if !get_status(KERNEL_D_LOAD) {
        return kernel_ged_load();
    }

    let buf = read_file(KERNEL_D_LOAD, 32)?;
    let parts: Vec<&str> = buf.split_whitespace().collect();

    if parts.len() >= 3 {
        if let Ok(idle) = parts[2].parse::<i32>() {
            let load = 100 - idle;
            debug!("dbggedload {load}");
            return Ok(if 100 - idle == 0 {
                kernel_ged_load()?
            } else {
                100 - idle
            });
        }
    }

    kernel_ged_load()
}

fn kernel_d_ged_load() -> Result<i32> {
    if !get_status(KERNEL_DEBUG_LOAD) {
        return kernel_debug_ged_load();
    }

    let buf = read_file(KERNEL_DEBUG_LOAD, 32)?;
    let parts: Vec<&str> = buf.split_whitespace().collect();

    if parts.len() >= 3 {
        if let Ok(idle) = parts[2].parse::<i32>() {
            let load = 100 - idle;
            debug!("dgedload {load}");
            return Ok(if 100 - idle == 0 {
                kernel_debug_ged_load()?
            } else {
                100 - idle
            });
        }
    }

    kernel_debug_ged_load()
}

fn mali_load() -> Result<i32> {
    if !get_status(PROC_MALI_LOAD) {
        return kernel_d_ged_load();
    }

    let buf = read_file(PROC_MALI_LOAD, 256)?;

    // Parse "gpu/cljs0/cljs1=XX" format
    if let Some(pos) = buf.find('=') {
        if let Ok(load) = buf[pos + 1..].trim().parse::<i32>() {
            debug!("mali {load}");
            return Ok(if load == 0 {
                kernel_d_ged_load()?
            } else {
                load
            });
        }
    }

    kernel_d_ged_load()
}

fn mtk_load() -> Result<i32> {
    if !get_status(PROC_MTK_LOAD) {
        return mali_load();
    }

    let buf = read_file(PROC_MTK_LOAD, 256)?;

    // Parse "ACTIVE=XX" format
    if let Some(pos) = buf.find("ACTIVE=") {
        if let Ok(load) = buf[pos + 7..].trim().parse::<i32>() {
            debug!("mtk_mali {load}");
            return Ok(if load == 0 { mali_load()? } else { load });
        }
    }

    mali_load()
}

fn gpufreq_load() -> Result<i32> {
    if !get_status(GPU_FREQ_LOAD_PATH) {
        return mtk_load();
    }

    let file = match File::open(GPU_FREQ_LOAD_PATH) {
        Ok(file) => file,
        Err(_) => {
            write_status(GPU_FREQ_LOAD_PATH, false);
            return Ok(0);
        }
    };

    let reader = BufReader::new(file);

    for line in reader.lines() {
        let line = line?;

        // Parse "gpu_loading = XX" format
        if let Some(pos) = line.find("gpu_loading = ") {
            if let Ok(load) = line[pos + 14..].trim().parse::<i32>() {
                debug!("gpufreq {load}");
                return Ok(if load == 0 { mtk_load()? } else { load });
            }
        }
    }

    mtk_load()
}

fn debug_dvfs_load_func() -> Result<i32> {
    // Check if debug_dvfs_load or debug_dvfs_load_old exists
    let path = if get_status(DEBUG_DVFS_LOAD) {
        DEBUG_DVFS_LOAD
    } else if get_status(DEBUG_DVFS_LOAD_OLD) {
        DEBUG_DVFS_LOAD_OLD
    } else {
        return gpufreq_load();
    };

    let buf = read_file(path, 256)?;
    let lines: Vec<&str> = buf.lines().collect();

    if lines.len() < 2 {
        return gpufreq_load();
    }

    // Static variables to keep track of previous values
    static mut PREV_BUSY: i64 = 0;
    static mut PREV_IDLE: i64 = 0;
    static mut PREV_PROTM: i64 = 0;

    // Parse the second line which contains the values
    let parts: Vec<&str> = lines[1].split_whitespace().collect();

    if parts.len() >= 3 {
        if let (Ok(busy), Ok(idle), Ok(protm)) = (
            parts[0].parse::<i64>(),
            parts[1].parse::<i64>(),
            parts[2].parse::<i64>(),
        ) {
            // Get previous values safely
            let (prev_busy, prev_idle, prev_protm) = unsafe { (PREV_BUSY, PREV_IDLE, PREV_PROTM) };

            // Calculate differences
            let diff_busy = busy - prev_busy;
            let diff_idle = idle - prev_idle;
            let diff_protm = protm - prev_protm;

            // Update previous values
            unsafe {
                PREV_BUSY = busy;
                PREV_IDLE = idle;
                PREV_PROTM = protm;
            }

            // Calculate load percentage
            let total = diff_busy + diff_idle + diff_protm;
            if total > 0 {
                let load = ((diff_busy + diff_protm) * 100 / total) as i32;
                let load = if load < 0 { 0 } else { load };

                debug!("debugutil: {load} {diff_busy} {diff_idle} {diff_protm}");
                return Ok(if load == 0 { mtk_load()? } else { load });
            }
        }
    }

    gpufreq_load()
}

pub fn get_gpu_load() -> Result<i32> {
    debug_dvfs_load_func()
}

pub fn get_gpu_current_freq() -> Result<i64> {
    // 首先尝试从GPU_CURRENT_FREQ_PATH读取频率
    if get_status(GPU_CURRENT_FREQ_PATH) {
        let buf = match read_file(GPU_CURRENT_FREQ_PATH, 64) {
            Ok(content) => content,
            Err(e) => {
                debug!("Failed to read GPU_CURRENT_FREQ_PATH: {e}");
                write_status(GPU_CURRENT_FREQ_PATH, false);
                // 不立即返回，继续尝试其他路径
                String::new()
            }
        };

        if !buf.is_empty() {
            let parts: Vec<&str> = buf.split_whitespace().collect();

            // 读取第二个整数作为当前频率
            if parts.len() >= 2 {
                if let Ok(freq) = parts[1].parse::<i64>() {
                    debug!("Current GPU frequency from {GPU_CURRENT_FREQ_PATH}: {freq}");
                    return Ok(freq);
                } else {
                    debug!("Failed to parse second value as frequency from: {}", buf);
                }
            } else {
                debug!("Not enough values in GPU frequency file, content: {}", buf);
            }
        }
    } else {
        debug!("GPU current frequency path not available: {GPU_CURRENT_FREQ_PATH}");
    }

    // 如果无法从GPU_CURRENT_FREQ_PATH读取，尝试从GPU_DEBUG_CURRENT_FREQ_PATH读取
    if get_status(GPU_DEBUG_CURRENT_FREQ_PATH) {
        let buf = match read_file(GPU_DEBUG_CURRENT_FREQ_PATH, 64) {
            Ok(content) => content,
            Err(e) => {
                debug!("Failed to read GPU_DEBUG_CURRENT_FREQ_PATH: {e}");
                write_status(GPU_DEBUG_CURRENT_FREQ_PATH, false);
                // 不立即返回，继续尝试其他路径
                String::new()
            }
        };

        if !buf.is_empty() {
            let parts: Vec<&str> = buf.split_whitespace().collect();

            // 读取第二个整数作为当前频率
            if parts.len() >= 2 {
                if let Ok(freq) = parts[1].parse::<i64>() {
                    debug!("Current GPU frequency from {GPU_DEBUG_CURRENT_FREQ_PATH}: {freq}");
                    return Ok(freq);
                } else {
                    debug!("Failed to parse second value as frequency from: {}", buf);
                }
            } else {
                debug!("Not enough values in GPU frequency file, content: {}", buf);
            }
        }
    } else {
        debug!("GPU debug current frequency path not available: {GPU_DEBUG_CURRENT_FREQ_PATH}");
    }

    // 如果无法从前两个路径读取，尝试从GPU_FREQ_LOAD_PATH读取
    if get_status(GPU_FREQ_LOAD_PATH) {
        debug!("Trying to read frequency from {GPU_FREQ_LOAD_PATH}");

        let file = match File::open(GPU_FREQ_LOAD_PATH) {
            Ok(file) => file,
            Err(e) => {
                debug!("Failed to open GPU_FREQ_LOAD_PATH: {e}");
                write_status(GPU_FREQ_LOAD_PATH, false);
                // 如果所有路径都不可用，抛出异常
                return Err(anyhow!("Cannot read GPU frequency: all frequency paths are unavailable"));
            }
        };

        let reader = BufReader::new(file);

        for line in reader.lines() {
            let line = match line {
                Ok(l) => l,
                Err(e) => {
                    debug!("Error reading line from GPU_FREQ_LOAD_PATH: {e}");
                    continue;
                }
            };

            // 尝试解析"cur_freq = XX"格式
            if let Some(pos) = line.find("cur_freq = ") {
                if let Ok(freq) = line[pos + 11..].trim().parse::<i64>() {
                    debug!("Current GPU frequency from {GPU_FREQ_LOAD_PATH}: {freq}");
                    return Ok(freq);
                }
            }
        }
    } else {
        debug!("GPU frequency load path not available: {GPU_FREQ_LOAD_PATH}");
    }

    // 如果所有路径都不可用，抛出异常
    Err(anyhow!("Cannot read GPU frequency: all frequency paths are unavailable"))
}

pub fn utilization_init() -> Result<()> {
    let mut is_good = false;
    let mut freq_path_available = false;
    info!("Init LoadMonitor");
    info!("Testing GED...");

    // Method 1: Read From /sys/module/ged
    let module_load_status = check_read(MODULE_LOAD, &mut is_good);
    info!("{MODULE_LOAD}: {module_load_status}");
    let module_idle_status = check_read(MODULE_IDLE, &mut is_good);
    info!("{MODULE_IDLE}: {module_idle_status}");

    // Method 2: Read From /sys/kernel/ged
    let kernel_load_status = check_read(KERNEL_LOAD, &mut is_good);
    info!("{KERNEL_LOAD}: {kernel_load_status}");

    // Method 3: Read From /sys/kernel/debug/ged
    let kernel_debug_load_status = check_read(KERNEL_DEBUG_LOAD, &mut is_good);
    info!("{KERNEL_DEBUG_LOAD}: {kernel_debug_load_status}");
    let kernel_d_load_status = check_read(KERNEL_D_LOAD, &mut is_good);
    info!("{KERNEL_D_LOAD}: {kernel_d_load_status}");

    // 检查GPU频率路径
    info!("Testing GPU frequency paths...");
    let current_freq_status = check_read(GPU_CURRENT_FREQ_PATH, &mut freq_path_available);
    info!("{GPU_CURRENT_FREQ_PATH}: {current_freq_status}");

    // 检查GPU debug频率路径
    let debug_current_freq_status = check_read(GPU_DEBUG_CURRENT_FREQ_PATH, &mut freq_path_available);
    info!("{GPU_DEBUG_CURRENT_FREQ_PATH}: {debug_current_freq_status}");

    // Method 4: Read From /proc/gpufreq
    info!("Testing gpufreq Driver...");
    let freq_load_status = check_read(GPU_FREQ_LOAD_PATH, &mut freq_path_available);
    info!("{GPU_FREQ_LOAD_PATH}: {freq_load_status}");

    // Method 5: Read From Mali Driver
    info!("Testing mali driver ...");
    let proc_mtk_load_status = check_read(PROC_MTK_LOAD, &mut is_good);
    info!("{PROC_MTK_LOAD}: {proc_mtk_load_status}");
    let proc_mali_load_status = check_read(PROC_MALI_LOAD, &mut is_good);
    info!("{PROC_MALI_LOAD}: {proc_mali_load_status}");

    // Method 6: Read precise load from Mali Driver
    let debug_dvfs_load_status = check_read(DEBUG_DVFS_LOAD, &mut is_good);
    info!("{DEBUG_DVFS_LOAD}: {debug_dvfs_load_status}");
    let debug_dvfs_load_old_status = check_read(DEBUG_DVFS_LOAD_OLD, &mut is_good);
    info!("{DEBUG_DVFS_LOAD_OLD}: {debug_dvfs_load_old_status}");

    // 检查是否可以监控GPU负载
    if !is_good {
        error!("Can't Monitor GPU Loading!");
        return Err(anyhow!("Can't Monitor GPU Loading!"));
    }

    // 检查是否可以读取GPU频率
    if !freq_path_available {
        error!("Can't read GPU frequency: all paths ({GPU_CURRENT_FREQ_PATH}, {GPU_DEBUG_CURRENT_FREQ_PATH}, {GPU_FREQ_LOAD_PATH}) are unavailable!");
        return Err(anyhow!("Can't read GPU frequency: no valid frequency path available"));
    }

    info!("Test Finished.");
    Ok(())
}
